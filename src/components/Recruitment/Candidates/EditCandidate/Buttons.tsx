import React, { useState } from "react";
import { Box, Button, IconButton, FormHelperText, Tooltip } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";

interface ButtonsProps {
  resume: File | null;
  resumeURL: string | null;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
  error?: string;
  onGoBack: () => void;
  isFormValid: boolean;
  onSubmit: (e: React.FormEvent) => void;
  email?: string;
  fetchViewAttachmentsCandidate?: (data: { email: string }) => void;
}

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const extractFileName = (url: string): string => {
  if (url.includes(".pdf")) {
    const pdfMatch = url.match(/(_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}_\d{2}\.pdf|_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}\.pdf|\w+\.pdf)/i);
    if (pdfMatch) {
      return pdfMatch[0].replace(/^_/, '');
    }

    const simplePdfMatch = url.match(/[\w-]+\.pdf/i);
    if (simplePdfMatch) {
      return simplePdfMatch[0];
    }
  }

  // Handle Word documents
  if (url.includes(".doc")) {
    const docMatch = url.match(/(_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}_\d{2}\.docx?|_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}\.docx?|\w+\.docx?)/i);
    if (docMatch) {
      return docMatch[0].replace(/^_/, '');
    }

    const simpleDocMatch = url.match(/[\w-]+\.docx?/i);
    if (simpleDocMatch) {
      return simpleDocMatch[0];
    }
  }

  const pathSegments = url.split('/');
  const fileName = pathSegments[pathSegments.length - 1].split('?')[0];

  // If no extension found, default based on content type or assume PDF
  if (!fileName.includes('.')) {
    return fileName + '.pdf';
  }

  return fileName || 'resume.pdf';
};

const getFileExtension = (url: string, fileName: string): string => {
  // First try to get extension from filename
  if (fileName && fileName.includes('.')) {
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  // Then try to get from URL
  if (url.includes('.pdf')) return 'pdf';
  if (url.includes('.doc') && !url.includes('.docx')) return 'doc';
  if (url.includes('.docx')) return 'docx';

  return 'pdf'; // default
};

const downloadFile = (url: string, fileName: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const Buttons: React.FC<ButtonsProps> = ({
  resume,
  resumeURL,
  onUpload,
  onRemove,
  error,
  onGoBack,
  isFormValid,
  onSubmit,
  email,
  fetchViewAttachmentsCandidate,
}) => {
  const [uploadError, setUploadError] = useState<string>("");

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const allowedTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ];

      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only PDF or Word files are allowed.");
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        setUploadError("File size should not exceed 2MB.");
        return;
      }

      setUploadError("");
      onUpload(e);
    }
  };

  const handleResumeClick = (e: React.MouseEvent) => {
    if (!resume && resumeURL && email && fetchViewAttachmentsCandidate) {
      e.preventDefault();
      fetchViewAttachmentsCandidate({ email });
    } else if (resume || resumeURL) {
      e.preventDefault();
      const fileUrl = resume ? URL.createObjectURL(resume) : resumeURL;
      const fileName = displayFileName;

      if (fileUrl) {
        const fileExtension = getFileExtension(fileUrl, fileName);

        if (fileExtension === 'pdf') {
          // Open PDFs in new tab
          window.open(fileUrl, '_blank');
        } else if (fileExtension === 'doc' || fileExtension === 'docx') {
          // Download Word documents directly
          downloadFile(fileUrl, fileName || `resume.${fileExtension}`);
        } else {
          // Default behavior for other file types
          window.open(fileUrl, '_blank');
        }
      }
    }
  };

  const displayFileName = resume 
    ? resume.name 
    : resumeURL 
      ? extractFileName(resumeURL) 
      : "";

  const truncatedFileName = displayFileName.length > 15 
    ? displayFileName.slice(0, 12) + "..." 
    : displayFileName;

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mt: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Button
          component="label"
          variant="contained"
          startIcon={<CloudUploadIcon />}
          sx={{
            backgroundColor: "#193c6d",
            color: "white",
            padding: "10px 20px",
            fontSize: "14px",
            borderRadius: "24px",
          }}
        >
          Upload Resume
          <VisuallyHiddenInput type="file" accept=".pdf,.doc,.docx" onChange={handleFileUpload} />
        </Button>
        {uploadError && <FormHelperText error>{uploadError}</FormHelperText>}
        {(resume || resumeURL) && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              maxWidth: "150px",
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
            }}
          >
            <Tooltip title={displayFileName}>
              <a
                href="#"
                onClick={handleResumeClick}
                style={{ textDecoration: "none", color: "inherit", cursor: "pointer" }}
              >
                {truncatedFileName}
              </a>
            </Tooltip>
            <IconButton onClick={onRemove} size="small">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        )}
      </Box>
      <Box sx={{ display: "flex", gap: "1rem" }}>
        <Button
          variant="contained"
          onClick={onGoBack}
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: "#e2e2e2",
            color: "black",
            width: "120px",
            "&:hover": { bgcolor: "#e2e2e2", color: "black" },
            "&:active": { bgcolor: "#e2e2e2" },
          }}
        >
          CANCEL
        </Button>
        <Button
          variant="contained"
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: isFormValid ? "#193c6d" : "#e2e2e2",
            color: isFormValid ? "white" : "#a0a0a0",
            width: "120px",
            "&.Mui-disabled": {
              bgcolor: "#e2e2e2",
              color: "#a0a0a0",
            },
          }}
          type="submit"
          onClick={onSubmit}
          disabled={!isFormValid}
        >
          UPDATE
        </Button>
      </Box>
      {error && <FormHelperText error>{error}</FormHelperText>}
    </Box>
  );
};

export default Buttons;