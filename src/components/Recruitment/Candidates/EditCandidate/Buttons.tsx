import React, { useState } from "react";
import { Box, Button, IconButton, FormHelperText, Tooltip } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";

interface ButtonsProps {
  resume: File | null;
  resumeURL: string | null;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
  error?: string;
  onGoBack: () => void;
  isFormValid: boolean;
  onSubmit: (e: React.FormEvent) => void;
  email?: string;
  fetchViewAttachmentsCandidate?: (data: { email: string }) => void;
}

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const extractFileName = (url: string): string => {
  if (url.includes(".pdf")) {
    const pdfMatch = url.match(/(_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}_\d{2}\.pdf|_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}\.pdf|\w+\.pdf)/i);
    if (pdfMatch) {
      return pdfMatch[0].replace(/^_/, '');
    }

    const simplePdfMatch = url.match(/[\w-]+\.pdf/i);
    if (simplePdfMatch) {
      return simplePdfMatch[0];
    }
  }

  // Handle Word documents
  if (url.includes(".doc")) {
    const docMatch = url.match(/(_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}_\d{2}\.docx?|_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}\.docx?|\w+\.docx?)/i);
    if (docMatch) {
      return docMatch[0].replace(/^_/, '');
    }

    const simpleDocMatch = url.match(/[\w-]+\.docx?/i);
    if (simpleDocMatch) {
      return simpleDocMatch[0];
    }
  }

  const pathSegments = url.split('/');
  const fileName = pathSegments[pathSegments.length - 1].split('?')[0];

  // If no extension found, default based on content type or assume PDF
  if (!fileName.includes('.')) {
    return fileName + '.pdf';
  }

  return fileName || 'resume.pdf';
};

const getFileExtension = (url: string, fileName: string): string => {
  // First try to get extension from filename
  if (fileName && fileName.includes('.')) {
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  // Then try to get from URL
  if (url.includes('.pdf')) return 'pdf';
  if (url.includes('.doc') && !url.includes('.docx')) return 'doc';
  if (url.includes('.docx')) return 'docx';

  return 'pdf'; // default
};

const ensureProperFileName = (fileName: string, fileExtension: string): string => {
  if (!fileName) {
    return `resume.${fileExtension}`;
  }

  // If filename already has the correct extension, return as is
  if (fileName.toLowerCase().endsWith(`.${fileExtension}`)) {
    return fileName;
  }

  // If filename has a different extension, replace it
  if (fileName.includes('.')) {
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    return `${nameWithoutExt}.${fileExtension}`;
  }

  // If no extension, add the correct one
  return `${fileName}.${fileExtension}`;
};

const downloadFile = async (url: string, fileName: string) => {
  try {
    // Fetch the file as a blob to ensure proper handling
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/octet-stream, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/msword, application/pdf, */*',
      },
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();

    // Create a blob URL with the correct MIME type
    const fileExtension = fileName.split('.').pop()?.toLowerCase();
    let mimeType = blob.type;

    // Ensure correct MIME type for Word documents
    if (fileExtension === 'docx') {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileExtension === 'doc') {
      mimeType = 'application/msword';
    } else if (fileExtension === 'pdf') {
      mimeType = 'application/pdf';
    }

    // Create a new blob with the correct MIME type
    const correctBlob = new Blob([blob], { type: mimeType });
    const blobUrl = URL.createObjectURL(correctBlob);

    // Create download link
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = fileName;
    link.style.display = 'none';

    // Add to DOM, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the blob URL
    setTimeout(() => {
      URL.revokeObjectURL(blobUrl);
    }, 100);

  } catch (error) {
    console.error('Error downloading file:', error);
    // Fallback to simple download
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const Buttons: React.FC<ButtonsProps> = ({
  resume,
  resumeURL,
  onUpload,
  onRemove,
  error,
  onGoBack,
  isFormValid,
  onSubmit,
  email,
  fetchViewAttachmentsCandidate,
}) => {
  const [uploadError, setUploadError] = useState<string>("");

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const allowedTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ];

      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only PDF or Word files are allowed.");
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        setUploadError("File size should not exceed 2MB.");
        return;
      }

      setUploadError("");
      onUpload(e);
    }
  };

  const handleResumeClick = async (e: React.MouseEvent) => {
    if (!resume && resumeURL && email && fetchViewAttachmentsCandidate) {
      e.preventDefault();
      fetchViewAttachmentsCandidate({ email });
    } else if (resume || resumeURL) {
      e.preventDefault();
      const fileUrl = resume ? URL.createObjectURL(resume) : resumeURL;
      const fileName = displayFileName;

      if (fileUrl) {
        const fileExtension = getFileExtension(fileUrl, fileName);
        const properFileName = ensureProperFileName(fileName, fileExtension);

        if (fileExtension === 'pdf') {
          // Open PDFs in new tab
          window.open(fileUrl, '_blank');
        } else if (fileExtension === 'doc' || fileExtension === 'docx') {
          // Download Word documents directly with proper MIME type handling
          await downloadFile(fileUrl, properFileName);
        } else {
          // Default behavior for other file types
          window.open(fileUrl, '_blank');
        }
      }
    }
  };

  const displayFileName = resume 
    ? resume.name 
    : resumeURL 
      ? extractFileName(resumeURL) 
      : "";

  const truncatedFileName = displayFileName.length > 15 
    ? displayFileName.slice(0, 12) + "..." 
    : displayFileName;

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mt: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Button
          component="label"
          variant="contained"
          startIcon={<CloudUploadIcon />}
          sx={{
            backgroundColor: "#193c6d",
            color: "white",
            padding: "10px 20px",
            fontSize: "14px",
            borderRadius: "24px",
          }}
        >
          Upload Resume
          <VisuallyHiddenInput type="file" accept=".pdf,.doc,.docx" onChange={handleFileUpload} />
        </Button>
        {uploadError && <FormHelperText error>{uploadError}</FormHelperText>}
        {(resume || resumeURL) && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              maxWidth: "150px",
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
            }}
          >
            <Tooltip title={displayFileName}>
              <a
                href="#"
                onClick={handleResumeClick}
                style={{ textDecoration: "none", color: "inherit", cursor: "pointer" }}
              >
                {truncatedFileName}
              </a>
            </Tooltip>
            <IconButton onClick={onRemove} size="small">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        )}
      </Box>
      <Box sx={{ display: "flex", gap: "1rem" }}>
        <Button
          variant="contained"
          onClick={onGoBack}
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: "#e2e2e2",
            color: "black",
            width: "120px",
            "&:hover": { bgcolor: "#e2e2e2", color: "black" },
            "&:active": { bgcolor: "#e2e2e2" },
          }}
        >
          CANCEL
        </Button>
        <Button
          variant="contained"
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: isFormValid ? "#193c6d" : "#e2e2e2",
            color: isFormValid ? "white" : "#a0a0a0",
            width: "120px",
            "&.Mui-disabled": {
              bgcolor: "#e2e2e2",
              color: "#a0a0a0",
            },
          }}
          type="submit"
          onClick={onSubmit}
          disabled={!isFormValid}
        >
          UPDATE
        </Button>
      </Box>
      {error && <FormHelperText error>{error}</FormHelperText>}
    </Box>
  );
};

export default Buttons;