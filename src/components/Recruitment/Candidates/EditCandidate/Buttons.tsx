import React, { useState } from "react";
import { Box, Button, IconButton, FormHelperText, Tooltip } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";

interface ButtonsProps {
  resume: File | null;
  resumeURL: string | null;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
  error?: string;
  onGoBack: () => void;
  isFormValid: boolean;
  onSubmit: (e: React.FormEvent) => void;
  email?: string;
  fetchViewAttachmentsCandidate?: (data: { email: string }) => void;
}

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const extractFileName = (url: string): string => {
  if (url.includes(".pdf")) {
    const pdfMatch = url.match(/(_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}_\d{2}\.pdf|_\d{4}_\d{2}_\d{2}-\d{2}_\d{2}\.pdf|\w+\.pdf)/i);
    if (pdfMatch) {
      return pdfMatch[0].replace(/^_/, '');
    }
    
    const simplePdfMatch = url.match(/[\w-]+\.pdf/i);
    if (simplePdfMatch) {
      return simplePdfMatch[0];
    }
  }
  
  const pathSegments = url.split('/');
  return pathSegments[pathSegments.length - 1].split('?')[0] || 'resume.pdf';
};

const Buttons: React.FC<ButtonsProps> = ({
  resume,
  resumeURL,
  onUpload,
  onRemove,
  error,
  onGoBack,
  isFormValid,
  onSubmit,
  email,
  fetchViewAttachmentsCandidate,
}) => {
  const [uploadError, setUploadError] = useState<string>("");

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      if (file.type !== "application/pdf") {
        setUploadError("Only PDF files are allowed.");
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        setUploadError("File size should not exceed 2MB.");
        return;
      }
      setUploadError("");
      onUpload(e);
    }
  };

  const handleResumeClick = (e: React.MouseEvent) => {
    if (!resume && resumeURL && email && fetchViewAttachmentsCandidate) {
      e.preventDefault();
      fetchViewAttachmentsCandidate({ email });
    }
  };

  const displayFileName = resume 
    ? resume.name 
    : resumeURL 
      ? extractFileName(resumeURL) 
      : "";

  const truncatedFileName = displayFileName.length > 15 
    ? displayFileName.slice(0, 12) + "..." 
    : displayFileName;

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mt: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Button
          component="label"
          variant="contained"
          startIcon={<CloudUploadIcon />}
          sx={{
            backgroundColor: "#193c6d",
            color: "white",
            padding: "10px 20px",
            fontSize: "14px",
            borderRadius: "24px",
          }}
        >
          Upload Resume
          <VisuallyHiddenInput type="file" accept=".pdf" onChange={handleFileUpload} />
        </Button>
        {uploadError && <FormHelperText error>{uploadError}</FormHelperText>}
        {(resume || resumeURL) && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              maxWidth: "150px",
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
            }}
          >
            <Tooltip title={displayFileName}>
              <a
                href={resume ? URL.createObjectURL(resume) : resumeURL || '#'}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleResumeClick}
                style={{ textDecoration: "none", color: "inherit" }}
              >
                {truncatedFileName}
              </a>
            </Tooltip>
            <IconButton onClick={onRemove} size="small">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        )}
      </Box>
      <Box sx={{ display: "flex", gap: "1rem" }}>
        <Button
          variant="contained"
          onClick={onGoBack}
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: "#e2e2e2",
            color: "black",
            width: "120px",
            "&:hover": { bgcolor: "#e2e2e2", color: "black" },
            "&:active": { bgcolor: "#e2e2e2" },
          }}
        >
          CANCEL
        </Button>
        <Button
          variant="contained"
          sx={{
            padding: "12px 24px",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "24px",
            bgcolor: isFormValid ? "#193c6d" : "#e2e2e2",
            color: isFormValid ? "white" : "#a0a0a0",
            width: "120px",
            "&.Mui-disabled": {
              bgcolor: "#e2e2e2",
              color: "#a0a0a0",
            },
          }}
          type="submit"
          onClick={onSubmit}
          disabled={!isFormValid}
        >
          UPDATE
        </Button>
      </Box>
      {error && <FormHelperText error>{error}</FormHelperText>}
    </Box>
  );
};

export default Buttons;